"use client"

import { motion } from "framer-motion"
import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { 
  Code, 
  Terminal, 
  Book,  
  Copy, 
  Check,
  ArrowRight,
  Github,
  FileText,
  Play
} from "lucide-react"

export function DevelopersSection() {
  const [copiedCode, setCopiedCode] = useState<string | null>(null)

  const copyToClipboard = (code: string, id: string) => {
    navigator.clipboard.writeText(code)
    setCopiedCode(id)
    setTimeout(() => setCopiedCode(null), 2000)
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  }

  const connectCode = `// Initialize Crefy Connect SDK
import { CrefyConnect } from '@crefy/connect-sdk'

const connect = new CrefyConnect({
  projectId: 'your-project-id',
  chains: ['ethereum', 'polygon'],
  ensSubdomain: 'yourapp.crefy.eth'
})

// Social login with embedded wallet
const user = await connect.auth.login({
  provider: 'google', // twitter, discord, email, phone
  redirectUri: 'https://yourapp.com/callback'
})

// Access user's smart wallet
const wallet = user.wallet
const balance = await wallet.getBalance()
const address = wallet.getAddress()

// Send transaction (gasless with AA)
const tx = await wallet.sendTransaction({
  to: '0x...',
  value: '0.1',
  data: '0x...'
})

console.log(\`Transaction sent: \${tx.hash}\`)`

  const resources = [
    {
      icon: Book,
      title: "API Documentation",
      description: "Comprehensive guides and references for all endpoints",
      link: "/docs/api",
      color: "purple"
    },
    {
      icon: Code,
      title: "SDK Libraries",
      description: "JavaScript, Python, and React SDKs with TypeScript support",
      link: "/docs/sdks",
      color: "indigo"
    },
    {
      icon: Terminal,
      title: "CLI Tools",
      description: "Command-line interface for rapid development and testing",
      link: "/docs/cli",
      color: "purple"
    },
    {
      icon: Github,
      title: "Code Examples",
      description: "Open-source examples and starter templates",
      link: "/examples",
      color: "indigo"
    }
  ]

  return (
    <section id="developers" className="py-24 bg-white relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="text-center mb-16"
        >
          <motion.h2
            variants={itemVariants}
            className="text-4xl md:text-5xl font-bold mb-6 text-black"
          >
            Built for
            <br />
            <span className="text-gradient">Developers</span>
          </motion.h2>
          <motion.p
            variants={itemVariants}
            className="text-xl text-black/70 max-w-3xl mx-auto"
          >
            Get started in minutes with Crefy Connect APIs. Build seamless Web3 authentication
            and smart wallet integration for your applications.
          </motion.p>
        </motion.div>

        {/* Code Examples */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="mb-20"
        >
          <motion.div variants={itemVariants}>
            <Card className="bg-white border-purple-200 shadow-lg">
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle className="text-black">
                    Social Login with Smart Wallets
                  </CardTitle>
                  <CardDescription className="text-black/60">
                    Implement seamless Web3 onboarding with Crefy Connect APIs
                  </CardDescription>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(connectCode, "connect")}
                  className="border-purple-200 text-purple-600 hover:bg-purple-50"
                >
                  {copiedCode === "connect" ? (
                    <Check className="w-4 h-4" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                </Button>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                  <pre className="text-sm text-gray-300">
                    <code>{connectCode}</code>
                  </pre>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>

        {/* Developer Resources */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="mb-16"
        >
          <motion.h3 
            variants={itemVariants}
            className="text-3xl font-bold text-center mb-12"
          >
            Developer Resources
          </motion.h3>
          
          <motion.div
            variants={containerVariants}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-6"
          >
            {resources.map((resource, index) => (
              <motion.div key={index} variants={itemVariants}>
                <Card className="h-full bg-white border-purple-200 hover:border-purple-400 transition-all duration-300 hover:shadow-lg group cursor-pointer">
                  <CardHeader>
                    <div className={`w-12 h-12 rounded-xl flex items-center justify-center mb-4 ${
                      resource.color === 'purple'
                        ? 'bg-purple-100 group-hover:bg-purple-200'
                        : 'bg-indigo-100 group-hover:bg-indigo-200'
                    } transition-colors duration-300`}>
                      <resource.icon className={`w-6 h-6 ${
                        resource.color === 'purple' ? 'text-purple-600' : 'text-indigo-600'
                      }`} />
                    </div>
                    <CardTitle className="text-black text-lg">
                      {resource.title}
                    </CardTitle>
                    <CardDescription className="text-black/60">
                      {resource.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center text-purple-600 group-hover:text-purple-700 transition-colors duration-300">
                      <span className="text-sm font-medium">Learn more</span>
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>

        {/* Quick Start */}
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="text-center"
        >
          <motion.div
            variants={itemVariants}
            className="bg-gradient-to-r from-purple-100 to-indigo-100 rounded-3xl p-8 md:p-12 border border-purple-200"
          >
            <h3 className="text-3xl md:text-4xl font-bold mb-4 text-black">
              Ready to Start Building?
            </h3>
            <p className="text-xl text-black/70 mb-8 max-w-2xl mx-auto">
              Join thousands of developers building with Crefy Connect.
              Get started with our free tier and scale as you grow.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg" 
                className="bg-gradient-purple hover:opacity-90 text-white px-8 py-4 text-lg font-semibold"
              >
                <Play className="mr-2 w-5 h-5" />
                Start Free Trial
              </Button>
              <Button 
                variant="outline" 
                size="lg"
                className="border-2 border-purple-400 text-purple-400 hover:bg-purple-400 hover:text-white px-8 py-4 text-lg font-semibold"
              >
                <FileText className="mr-2 w-5 h-5" />
                View Documentation
              </Button>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
