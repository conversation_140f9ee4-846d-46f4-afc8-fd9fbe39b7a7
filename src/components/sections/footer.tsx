"use client"

import { motion } from "framer-motion"
import { 
  Github, 
  Twitter, 
  Linkedin, 
  Mail, 
  Code,
  FileText,
  Users,
  Building
} from "lucide-react"

export function Footer() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  }

  const footerLinks = {
    products: [
      { name: "Crefy Phygital", href: "https://phygitals.crefy.xyz/" },
      { name: "Crefy Connect", href: "https://connect.crefy.xyz/" },
      { name: "Enterprise", href: "#contact" },
      { name: "Pricing", href: "#pricing" }
    ],
    developers: [
      { name: "Documentation", href: "https://docs.connect.crefy.xyz/" },
    ],
    company: [
      { name: "About", href: "/about" },
      { name: "Blog", href: "/blog" },
      { name: "Careers", href: "/careers" },
      { name: "Contact", href: "#contact" }
    ],
    legal: [
      { name: "Privacy Policy", href: "/privacy" },
      { name: "Terms of Service", href: "/terms" },
      { name: "Security", href: "/security" },
      { name: "Status", href: "/status" }
    ]
  }

  const socialLinks = [
    { icon: Twitter, href: "https://twitter.com/crefy", label: "Twitter" },
    { icon: Github, href: "https://github.com/crefy", label: "GitHub" },
    { icon: Linkedin, href: "https://linkedin.com/company/crefy", label: "LinkedIn" },
    { icon: Mail, href: "mailto:<EMAIL>", label: "Email" }
  ]

  return (
    <footer className="bg-white-900 text-black">

      {/* Main Footer */}
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={containerVariants}
        className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* Brand Section */}
          <motion.div variants={itemVariants} className="lg:col-span-2">
            <div className="text-3xl font-bold text-gradient mb-4">CREFY</div>
            <p className="text-black-400 mb-6 max-w-sm">
              Foundational identity infrastructure for the Internet of Value. 
              Powering next-gen interactions between people, products, and platforms.
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social, index) => (
                <motion.a
                  key={index}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-10 h-10 bg-white-800 rounded-lg flex items-center justify-center hover:bg-purple-600 transition-colors duration-300"
                  aria-label={social.label}
                >
                  <social.icon className="w-5 h-5" />
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Products */}
          <motion.div variants={itemVariants}>
            <h4 className="font-semibold text-black mb-4 flex items-center">
              <Code className="w-4 h-4 mr-2" />
              Products
            </h4>
            <ul className="space-y-3">
              {footerLinks.products.map((link, index) => (
                <li key={index}>
                  <a
                    href={link.href}
                    className="text-black-400 hover:text-purple-400 transition-colors duration-200"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Developers */}
          <motion.div variants={itemVariants}>
            <h4 className="font-semibold text-black mb-4 flex items-center">
              <FileText className="w-4 h-4 mr-2" />
              Developers
            </h4>
            <ul className="space-y-3">
              {footerLinks.developers.map((link, index) => (
                <li key={index}>
                  <a
                    href={link.href}
                    className="text-black-400 hover:text-purple-400 transition-colors duration-200"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Company */}
          <motion.div variants={itemVariants}>
            <h4 className="font-semibold text-black mb-4 flex items-center">
              <Building className="w-4 h-4 mr-2" />
              Company
            </h4>
            <ul className="space-y-3">
              {footerLinks.company.map((link, index) => (
                <li key={index}>
                  <a
                    href={link.href}
                    className="text-black-400 hover:text-purple-400 transition-colors duration-200"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Legal */}
          <motion.div variants={itemVariants}>
            <h4 className="font-semibold text-black mb-4 flex items-center">
              <Users className="w-4 h-4 mr-2" />
              Legal
            </h4>
            <ul className="space-y-3">
              {footerLinks.legal.map((link, index) => (
                <li key={index}>
                  <a
                    href={link.href}
                    className="text-black-400 hover:text-purple-400 transition-colors duration-200"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </motion.div>
        </div>
      </motion.div>

      {/* Bottom Bar */}
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={itemVariants}
        className="border-t border-black-800"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-black-400 text-sm">
              © 2025 CREFY. All rights reserved.
            </p>
            <div className="flex items-center space-x-6 mt-4 md:mt-0">
              <span className="text-black-400 text-sm">Built with ❤️ for developers</span>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-black-400 text-sm">All systems operational</span>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </footer>
  )
}
