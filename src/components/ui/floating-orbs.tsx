"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { useScrollAnimations } from "@/hooks/use-scroll-animations"

interface FloatingOrbsProps {
  count?: number
  className?: string
  enableShapes?: boolean
}

export function FloatingOrbs({ count = 8, className = "", enableShapes = true }: FloatingOrbsProps) {
  const { parallaxSlow, parallaxMedium, parallaxFast } = useScrollAnimations()
  const [orbs, setOrbs] = useState<any[]>([])

  useEffect(() => {
    const colors = [
      'rgba(107, 33, 168, 0.3)', // Deep purple
      'rgba(124, 58, 237, 0.25)', // Royal purple
      'rgba(79, 70, 229, 0.2)', // Indigo
      'rgba(99, 102, 241, 0.15)', // Light indigo
      'rgba(139, 92, 246, 0.2)', // Violet
    ]

    const shapes = enableShapes ? ['circle', 'ellipse', 'rounded-square', 'diamond'] : ['circle']
    const animationClasses = ['floating-orb-1', 'floating-orb-2', 'floating-orb-3', 'floating-orb-4', 'floating-orb-5']
    const parallaxSpeeds = ['slow', 'medium', 'fast']

    const generateOrbs = () => {
      const newOrbs = []
      for (let i = 0; i < count; i++) {
        newOrbs.push({
          id: i,
          size: Math.random() * 200 + 50, // 50-250px
          x: Math.random() * 100,
          y: Math.random() * 100,
          color: colors[Math.floor(Math.random() * colors.length)],
          animationClass: animationClasses[Math.floor(Math.random() * animationClasses.length)],
          opacity: Math.random() * 0.4 + 0.1, // 0.1-0.5
          delay: Math.random() * 5,
          parallaxSpeed: parallaxSpeeds[Math.floor(Math.random() * parallaxSpeeds.length)],
          shape: shapes[Math.floor(Math.random() * shapes.length)]
        })
      }
      setOrbs(newOrbs)
    }

    generateOrbs()
  }, [count, enableShapes])

  const getParallaxTransform = (speed: string) => {
    switch (speed) {
      case 'slow': return parallaxSlow
      case 'medium': return parallaxMedium
      case 'fast': return parallaxFast
      default: return parallaxSlow
    }
  }

  const getShapeStyles = (shape: string, size: number) => {
    const baseStyles = {
      width: `${size}px`,
      height: `${size}px`,
    }

    switch (shape) {
      case 'ellipse':
        return {
          ...baseStyles,
          borderRadius: '50%',
          transform: 'scaleX(1.5)'
        }
      case 'rounded-square':
        return {
          ...baseStyles,
          borderRadius: '25%'
        }
      case 'diamond':
        return {
          ...baseStyles,
          borderRadius: '0',
          transform: 'rotate(45deg)'
        }
      default: // circle
        return {
          ...baseStyles,
          borderRadius: '50%'
        }
    }
  }

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {orbs.map((orb) => (
        <motion.div
          key={orb.id}
          className={`floating-orb ${orb.animationClass}`}
          style={{
            ...getShapeStyles(orb.shape, orb.size),
            left: `${orb.x}%`,
            top: `${orb.y}%`,
            backgroundColor: orb.color,
            opacity: orb.opacity,
            animationDelay: `${orb.delay}s`,
            transform: 'translate(-50%, -50%)',
            y: getParallaxTransform(orb.parallaxSpeed)
          }}
        />
      ))}
    </div>
  )
}
