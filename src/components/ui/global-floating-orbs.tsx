"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { useScrollAnimations } from "@/hooks/use-scroll-animations"

interface GlobalFloatingOrbsProps {
  count?: number
}

export function GlobalFloatingOrbs({ count = 20 }: GlobalFloatingOrbsProps) {
  const { parallaxSlow, parallaxMedium, parallaxFast } = useScrollAnimations()
  const [orbs, setOrbs] = useState<any[]>([])

  useEffect(() => {
    const colors = [
      'rgba(107, 33, 168, 0.15)', // Deep purple - very subtle
      'rgba(124, 58, 237, 0.12)', // Royal purple
      'rgba(79, 70, 229, 0.1)', // Indigo
      'rgba(99, 102, 241, 0.08)', // Light indigo
      'rgba(139, 92, 246, 0.1)', // Violet
    ]

    const shapes = ['circle', 'ellipse', 'rounded-square', 'diamond', 'triangle']
    const animationClasses = ['floating-orb-1', 'floating-orb-2', 'floating-orb-3', 'floating-orb-4', 'floating-orb-5']
    const parallaxSpeeds = ['slow', 'medium', 'fast']
    const sizeCategories = ['tiny', 'small', 'medium', 'large', 'huge']

    const getSizeFromCategory = (category: string) => {
      switch (category) {
        case 'tiny': return Math.random() * 30 + 20 // 20-50px
        case 'small': return Math.random() * 50 + 50 // 50-100px
        case 'medium': return Math.random() * 80 + 100 // 100-180px
        case 'large': return Math.random() * 120 + 180 // 180-300px
        case 'huge': return Math.random() * 200 + 300 // 300-500px
        default: return Math.random() * 100 + 50
      }
    }

    const generateOrbs = () => {
      const newOrbs = []
      for (let i = 0; i < count; i++) {
        const sizeCategory = sizeCategories[Math.floor(Math.random() * sizeCategories.length)]
        newOrbs.push({
          id: i,
          size: getSizeFromCategory(sizeCategory),
          x: Math.random() * 120 - 10, // Allow orbs to go slightly off-screen
          y: Math.random() * 500, // Spread across multiple viewport heights
          color: colors[Math.floor(Math.random() * colors.length)],
          animationClass: animationClasses[Math.floor(Math.random() * animationClasses.length)],
          opacity: Math.random() * 0.25 + 0.05, // 0.05-0.3 for very subtle effect
          delay: Math.random() * 10,
          parallaxSpeed: parallaxSpeeds[Math.floor(Math.random() * parallaxSpeeds.length)],
          shape: shapes[Math.floor(Math.random() * shapes.length)],
          sizeCategory
        })
      }
      setOrbs(newOrbs)
    }

    generateOrbs()
  }, [count])

  const getParallaxTransform = (speed: string) => {
    switch (speed) {
      case 'slow': return parallaxSlow
      case 'medium': return parallaxMedium
      case 'fast': return parallaxFast
      default: return parallaxSlow
    }
  }

  const getShapeStyles = (shape: string, size: number) => {
    const baseStyles = {
      width: `${size}px`,
      height: `${size}px`,
    }

    switch (shape) {
      case 'ellipse':
        return {
          ...baseStyles,
          borderRadius: '50%',
          transform: 'scaleX(1.8) scaleY(0.6)'
        }
      case 'rounded-square':
        return {
          ...baseStyles,
          borderRadius: '30%'
        }
      case 'diamond':
        return {
          ...baseStyles,
          borderRadius: '0',
          transform: 'rotate(45deg)'
        }
      case 'triangle':
        return {
          ...baseStyles,
          borderRadius: '0',
          clipPath: 'polygon(50% 0%, 0% 100%, 100% 100%)'
        }
      default: // circle
        return {
          ...baseStyles,
          borderRadius: '50%'
        }
    }
  }

  return (
    <div className="fixed inset-0 overflow-hidden pointer-events-none z-0">
      {orbs.map((orb) => (
        <motion.div
          key={orb.id}
          className={`floating-orb ${orb.animationClass}`}
          style={{
            ...getShapeStyles(orb.shape, orb.size),
            left: `${orb.x}%`,
            top: `${orb.y}vh`,
            backgroundColor: orb.color,
            opacity: orb.opacity,
            animationDelay: `${orb.delay}s`,
            transform: 'translate(-50%, -50%)',
            y: getParallaxTransform(orb.parallaxSpeed)
          }}
        />
      ))}
    </div>
  )
}
