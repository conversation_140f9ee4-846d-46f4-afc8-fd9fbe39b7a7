import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "CREFY - Foundational Identity Infrastructure for the Internet of Value",
  description: "Modular, developer-first identity tools that enable seamless authentication, connection, and governance for users across on-chain and off-chain experiences. Featuring Crefy Phygital for tokenized physical products and Crefy Connect for social login with embedded smart wallets.",
  keywords: ["identity infrastructure", "Web3", "tokenization", "smart wallets", "phygital", "blockchain", "authentication", "developer tools"],
  authors: [{ name: "CREFY Team" }],
  creator: "CREFY",
  publisher: "CREFY",
  openGraph: {
    title: "CREFY - Foundational Identity Infrastructure",
    description: "Build the future of identity with CREFY's modular platform for tokenized products and seamless Web3 onboarding.",
    url: "https://crefy.com",
    siteName: "CREFY",
    type: "website",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "CREFY - Identity Infrastructure Platform",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "CREFY - Foundational Identity Infrastructure",
    description: "Build the future of identity with CREFY's modular platform for tokenized products and seamless Web3 onboarding.",
    images: ["/og-image.png"],
    creator: "@crefy",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
