@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #7c3aed;
  --primary-foreground: #ffffff;
  --deep-purple: #6b21a8;
  --indigo: #4338ca;
  --royal-purple: #7c3aed;
  --secondary: #ffffff;
  --secondary-foreground: #0f172a;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --accent: #f8fafc;
  --accent-foreground: #0f172a;
  --border: #e5e7eb;
  --input: #f9fafb;
  --ring: #7c3aed;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: #000000; /* Pure black text for better readability */
  font-family: var(--font-geist-sans), system-ui, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
  overflow-x: hidden; /* Prevent horizontal scroll from floating orbs */
}

/* Custom utility classes */
.text-gradient {
  background: linear-gradient(135deg, #7c3aed 0%, #4f46e5 50%, #6366f1 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.bg-gradient-purple {
  background: linear-gradient(135deg, #7c3aed 0%, #4f46e5 50%, #6366f1 100%);
}

.border-gradient {
  border: 1px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #7c3aed, #4f46e5, #6366f1) border-box;
}

/* Floating Orb Animations */
@keyframes float-slow {
  0%, 100% { transform: translate(-50%, -50%) translateY(0px) rotate(0deg); }
  25% { transform: translate(-50%, -50%) translateY(-20px) rotate(90deg); }
  50% { transform: translate(-50%, -50%) translateY(-10px) rotate(180deg); }
  75% { transform: translate(-50%, -50%) translateY(-30px) rotate(270deg); }
}

@keyframes float-medium {
  0%, 100% { transform: translate(-50%, -50%) translateY(0px) rotate(0deg); }
  33% { transform: translate(-50%, -50%) translateY(-25px) rotate(120deg); }
  66% { transform: translate(-50%, -50%) translateY(-15px) rotate(240deg); }
}

@keyframes float-fast {
  0%, 100% { transform: translate(-50%, -50%) translateY(0px) rotate(0deg); }
  50% { transform: translate(-50%, -50%) translateY(-35px) rotate(180deg); }
}

.floating-orb {
  filter: blur(40px);
  mix-blend-mode: multiply;
  pointer-events: none;
  z-index: 0;
  border-radius: 50%;
  position: absolute;
  will-change: transform;
  transform: translateZ(0);
}

.floating-orb-1 {
  animation: float-slow 20s ease-in-out infinite;
}

.floating-orb-2 {
  animation: float-medium 15s ease-in-out infinite reverse;
}

.floating-orb-3 {
  animation: float-fast 12s ease-in-out infinite;
}

.floating-orb-4 {
  animation: float-slow 18s ease-in-out infinite reverse;
}

.floating-orb-5 {
  animation: float-medium 22s ease-in-out infinite;
}

/* Performance optimizations */
.hover-lift, .hover-scale, .hover-glow {
  will-change: transform, box-shadow;
}

.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
