"use client"

import { useScroll, useTransform } from "framer-motion"

export function useScrollAnimations() {
  const { scrollY } = useScroll()
  
  // Parallax transforms for different speeds
  const parallaxSlow = useTransform(scrollY, [0, 1000], [0, -100])
  const parallaxMedium = useTransform(scrollY, [0, 1000], [0, -200])
  const parallaxFast = useTransform(scrollY, [0, 1000], [0, -300])
  
  return {
    scrollY,
    parallaxSlow,
    parallaxMedium,
    parallaxFast
  }
}

export function useScrollReveal() {
  const { scrollY } = useScroll()
  
  const opacity = useTransform(scrollY, [0, 300], [0, 1])
  const y = useTransform(scrollY, [0, 300], [50, 0])
  
  return { opacity, y }
}

// Animation variants for consistent use across components
export const staggeredRevealVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
}

export const revealItemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6, ease: "easeOut" }
  }
}

export const enhancedHoverVariants = {
  initial: { scale: 1, y: 0 },
  hover: {
    scale: 1.02,
    y: -4,
    transition: { duration: 0.2, ease: "easeOut" }
  },
  tap: {
    scale: 0.98,
    transition: { duration: 0.1 }
  }
}

export const floatingVariants = {
  animate: {
    y: [-10, 10, -10],
    transition: {
      duration: 6,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
}
