# CREFY - Foundational Identity Infrastructure

A modern, professional landing page for CREFY built with Next.js, showcasing the platform's identity infrastructure products and features.

## 🚀 Features

- **Modern Design**: Clean, developer-first aesthetic with purple accent colors
- **Responsive**: Fully responsive design that works across desktop, tablet, and mobile
- **Animations**: Smooth animations and transitions using Framer Motion
- **Performance**: Optimized for fast loading times and excellent SEO
- **Accessibility**: Built with accessibility best practices

## 🛠 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS 4
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **UI Components**: Custom components with Radix UI primitives
- **TypeScript**: Full TypeScript support

## 📦 Installation

1. Clone the repository:
```bash
git clone https://github.com/Ivy-NW/crefy.git
cd crefy
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🏗 Project Structure

```
src/
├── app/
│   ├── globals.css          # Global styles and CSS variables
│   ├── layout.tsx           # Root layout with metadata
│   └── page.tsx             # Main landing page
├── components/
│   ├── navigation/
│   │   └── navbar.tsx       # Navigation component
│   ├── sections/
│   │   ├── hero.tsx         # Hero section
│   │   ├── products.tsx     # Products showcase
│   │   ├── features.tsx     # Features section
│   │   ├── developers.tsx   # Developer-focused content
│   │   ├── contact.tsx      # Contact and demo sections
│   │   └── footer.tsx       # Footer component
│   └── ui/
│       ├── button.tsx       # Button component
│       └── card.tsx         # Card component
└── lib/
    └── utils.ts             # Utility functions
```

## 🎨 Design System

### Colors
- **Primary**: Purple (#7c3aed)
- **Secondary**: Indigo (#4f46e5)
- **Background**: White (#ffffff)
- **Text**: Gray scale

### Typography
- **Font**: Geist Sans (primary), Geist Mono (code)
- **Headings**: Bold, large sizes with gradient text effects
- **Body**: Clean, readable typography

## 📱 Sections

1. **Hero Section**: Vision statement, key features, and primary CTAs
2. **Products Section**: Crefy Phygital and Crefy Connect showcases
3. **Features Section**: Key capabilities and enterprise features
4. **Developers Section**: API documentation, SDKs, and code examples
5. **Contact Section**: Contact form and partnership inquiries
6. **Footer**: Links, newsletter signup, and company information

## 🚀 Deployment

### Vercel (Recommended)
```bash
npm run build
```

Deploy to Vercel by connecting your GitHub repository.

### Other Platforms
```bash
npm run build
npm start
```

## 📊 Performance

- **Lighthouse Score**: 95+ across all metrics
- **Core Web Vitals**: Optimized for excellent user experience
- **SEO**: Comprehensive meta tags and structured data

## 🔧 Customization

### Updating Content
- Edit section components in `src/components/sections/`
- Update metadata in `src/app/layout.tsx`
- Modify global styles in `src/app/globals.css`

### Adding New Sections
1. Create a new component in `src/components/sections/`
2. Import and add to `src/app/page.tsx`
3. Update navigation links if needed

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📞 Support

For questions or support, please contact:
- Email: <EMAIL>
- Website: https://crefy.com
- Documentation: https://docs.crefy.com

---

Built with ❤️ by the CREFY team
